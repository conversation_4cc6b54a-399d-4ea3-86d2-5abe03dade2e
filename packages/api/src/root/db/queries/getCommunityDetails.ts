import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"

type GetCommunityDetailsParams = {
  community: {
    id: number
    openness: string
    location: string
    online: string
  }
  loginData: {
    id: number
  }
}

export const getCommunityDetails = async ({
  community,
  loginData,
}: GetCommunityDetailsParams) => {
  let canSeeLocation = false

  if (
    hasPermission(loginData, "community", "view", {
      id: community.id,
      openness: community.openness,
    })
  ) {
    canSeeLocation = true
  }

  return {
    ...community,
    location: canSeeLocation ? community.location : undefined,
    online: canSeeLocation ? community.online : undefined,
    member: hasPermission(loginData, "community", "isMember", {
      id: community.id,
    }),
  }
}
