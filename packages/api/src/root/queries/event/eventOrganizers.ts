import { TRPCError } from "@trpc/server"
import { and, eq, or } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { eventProcedure } from "../../trpc/procedures/eventProcedure"

export const eventOrganizers = eventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, event } }) => {
    if (!hasPermission(loginData, "event", "view", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't view this event",
      })
    }

    const communities = await db
      .select({
        id: communitySchema.id,
        name: communitySchema.name,
        image: communitySchema.image,
        owner: communityToEventSchema.owner,
        location: communitySchema.location,
        online: communitySchema.online,
        openness: communitySchema.openness,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
      })
      .from(communityToEventSchema)
      .innerJoin(
        communitySchema,
        eq(communityToEventSchema.communityId, communitySchema.id),
      )
      .where(eq(communityToEventSchema.eventId, input.eventId))
      .then((data) => {
        return data
      })

    const hosts = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        avatar: usersSchema.avatar,
        color: usersSchema.color,
      })
      .from(permissionUserToRoleSchema)
      .innerJoin(
        usersSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .where(
        and(
          eq(permissionUserToRoleSchema.subjectId, input.eventId),
          eq(permissionUserToRoleSchema.subject, "event"),
          or(
            eq(permissionUserToRoleSchema.roleId, "host"),
            eq(permissionUserToRoleSchema.roleId, "cohost"),
          ),
        ),
      )
      .then((data) => {
        return data
      })

    return { communities, hosts }
  })
