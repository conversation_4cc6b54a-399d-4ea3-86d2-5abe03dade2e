import { Box } from "@mui/material"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { UserRow } from "../../../components/user/UserRow/UserRow"
import { membersRoute } from "../../../routes/community/members.route"
import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

export const MembersPage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const users = membersRoute.useLoaderData()

  if (!isCommunity(base) || !users) return null

  return (
    <>
      <TitleRow title="Members" />
      <Box
        alignContent="center"
        justifyContent="center"
        display="flex"
        flexDirection="row"
        gap={4}
        flexWrap="wrap"
        padding={5}
      >
        {users
          .sort((u1, u2) => (u1.name > u2.name ? 1 : -1))
          .map((user) => (
            <UserRow
              labelInfo="Member: "
              key={user.id}
              user={user}
              communityId={base.id.toString()}
            />
          ))}
      </Box>
    </>
  )
}
