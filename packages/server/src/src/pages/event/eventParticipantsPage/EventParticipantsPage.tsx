import { Box } from "@mui/material"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { UserRow } from "../../../components/user/UserRow/UserRow"
import { eventParticipantsRoute } from "../../../routes/event/eventParticipants.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

export const EventParticipantsPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const participants = eventParticipantsRoute.useLoaderData()

  if (!event || !isEvent(event) || !participants) {
    return null
  }

  return (
    <>
      <TitleRow title="Participants" />
      <Box
        alignContent="center"
        justifyContent="center"
        display="flex"
        flexDirection="row"
        gap={4}
        flexWrap="wrap"
        padding={5}
      >
        {participants?.map((participant) => (
          <UserRow
            user={participant}
            labelInfo="Participant: "
            key={participant.id}
            eventId={event.id}
          />
        ))}
      </Box>
    </>
  )
}
