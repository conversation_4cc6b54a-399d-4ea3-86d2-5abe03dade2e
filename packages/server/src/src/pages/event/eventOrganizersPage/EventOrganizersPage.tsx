import { Box, Typography } from "@mui/material"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { eventOrganizersRoute } from "../../../routes/event/eventOrganizers.route"
import { CommunityThumbnail } from "../../../components/community/CommunityThumbnail/CommunityThumbnail"

export const EventOrganizersPage = () => {
  const data = eventOrganizersRoute.useLoaderData()

  if (!data) {
    return null
  }

  const { communities, hosts } = data

  return (
    <Box>
      <TitleRow title="Organizers" />
      <Box>
        <Typography variant="h6">Communities</Typography>
        <Box>
          {communities.map((community) => (
            <CommunityThumbnail community={community} onClick={() => {}} />
          ))}
        </Box>
      </Box>
      <Box>
        <Typography variant="h6">Hosts</Typography>
        <Box>
          {hosts.map((host) => (

          ))}
        </Box>
      </Box>
    </Box>
  )
}
