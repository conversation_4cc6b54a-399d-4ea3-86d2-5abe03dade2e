import ReportGmailerrorredIcon from "@mui/icons-material/ReportGmailerrorred"
import { <PERSON>, Card, CardContent, Typography } from "@mui/material"

import { type RoleData } from "../../../../../../common/src/permissions/roles/helpers/types"
import {
  getCommunityRole,
  getEventRole,
  hasCommunityRole,
  hasEventRole,
  hasPermission,
} from "../../../permissions"
import {
  COMMUNITY_USER_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"
import { type ICAvatarUser, UserAvatar } from "../UserAvatar/UserAvatar"

import * as styles from "./useRow.module.css"

type ICUserRow = ICAvatarUser & {
  roles?: RoleData[]
  gameCount?: number | null
}

interface UserRowProps {
  user: Pick<
    ICUserRow,
    "id" | "name" | "avatar" | "color" | "roles" | "gameCount"
  >
  communityId?: string
  eventId?: number
  labelInfo: string
}
export const UserRow = ({
  user,
  communityId,
  eventId,
  labelInfo,
}: UserRowProps) => {
  const displayRole = communityId
    ? getCommunityRole(Number(communityId), user.roles ?? [])
    : getEventRole(eventId ?? 0, user.roles ?? [])

  const myData = useUserStore((store) => store.userData)
  return (
    <PartyLink
      to={communityId ? COMMUNITY_USER_ROUTE : EVENT_PARTICIPANT_ROUTE}
      params={{
        userId: user.id.toString(),
        participantId: user.id.toString(),
        communityId: String(communityId),
        eventId: String(eventId),
      }}
      title={user.name}
      aria-label={`Open ${user.name} profile`}
      className={styles.container}
    >
      <Card className={styles.card}>
        <CardContent>
          <Box display="flex" justifyContent="center">
            <UserAvatar
              labelInfo={labelInfo}
              size="large"
              user={user}
              noFocus
            />
          </Box>
          <Box
            pt={2}
            display="flex"
            justifyContent="center"
            alignItems="center"
            flexDirection="column"
          >
            <Typography variant="h6" className={styles.name}>
              {user.name}
            </Typography>
            {communityId && user.gameCount && (
              <Typography>{user.gameCount} games</Typography>
            )}
          </Box>
          <Box width="100%" textAlign="center" textTransform="uppercase">
            <Typography
              variant="subtitle1"
              color={
                displayRole === "host" || displayRole === "cohost"
                  ? "error"
                  : "info"
              }
            >
              {displayRole}
            </Typography>
          </Box>
        </CardContent>
        {communityId &&
          hasCommunityRole(
            user.roles ?? [],
            "invited",
            parseInt(communityId),
          ) &&
          hasPermission(myData, "community", "approve", {
            id: parseInt(communityId),
          }) && (
            <Box title="Requires approval" className={styles.invited}>
              <ReportGmailerrorredIcon color="info" fontSize="large" />
            </Box>
          )}
        {eventId &&
          hasEventRole(user.roles ?? [], "requested", eventId) &&
          hasPermission(myData, "event", "approve", {
            id: eventId,
          }) && (
            <Box title="Requires approval" className={styles.invited}>
              <ReportGmailerrorredIcon color="info" fontSize="large" />
            </Box>
          )}
      </Card>
    </PartyLink>
  )
}
